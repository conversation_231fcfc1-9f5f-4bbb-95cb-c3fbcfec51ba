<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>贷款服务方名单</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            background: #fff;
            display: flex;
            align-items: center;
            padding-right: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            z-index: 100;
        }

        .back-btn {
            width: 30px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn img {
            width: 8px;
            height: 14px;
        }

        .title {
            flex: 1;
            text-align: center;
            font-size: 17px;
            font-weight: 600;
            color: #000000;
            line-height: 24px;
            font-style: normal;
        }

        .list-content {
            padding: 56px 12px 12px;
        }

        .list-item {
            background: #fff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }

        .institution-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 12px;
        }

        .info-row {
            display: flex;
            font-size: 14px;
            margin-top: 8px;
        }

        .label {
            color: #666;
            width: 70px;
            flex-shrink: 0;
        }

        .value {
            color: #333;
            flex: 1;
        }

        .no-data {
            text-align: center;
            color: #999;
            font-size: 14px;
            padding: 40px 0;
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="back-btn" onclick="window.history.back()">
            <img src="./images/back.png" alt="返回">
        </div>
        <div class="title">贷款服务方名单</div>
    </div>

    <div class="list-content" id="listContent" />

    <script>
        // 获取列表数据
        async function fetchInstitutionList() {
            try {
                // 测试地址
                // const apiUrl = 'https://tapi.haoxincd.cn/channel/advertiserInfo/display/info'
                // 生产地址
                const apiUrl = 'https://api.haoxincd.cn/channel/advertiserInfo/display/info'
                
                const response = await fetch(apiUrl);
                const data = await response.json();

                if (data.code === 200) {
                    renderList(data.data || []);
                } else {
                    console.log(data.msg || '获取数据失败');
                }
            } catch (error) {
                console.error('加载数据失败:', error);
            }
        }

        // 渲染列表
        function renderList(list) {
            const listContent = document.getElementById('listContent');
            if (!list.length) {
                listContent.innerHTML = '<div class="no-data">暂无数据</div>';
                return;
            }

            const html = list.map(item => `
                <div class="list-item">
                    <div class="institution-name">${item.advertiserName}</div>
                    <div class="info-row">
                        <span class="label">机构电话</span>
                        <span class="value">${item.advertiserMobile}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">机构地址</span>
                        <span class="value">${item.advertiserAddress}</span>
                    </div>
                </div>
            `).join('');

            listContent.innerHTML = html;
        }

        document.addEventListener('DOMContentLoaded', fetchInstitutionList);
    </script>
</body>

</html>