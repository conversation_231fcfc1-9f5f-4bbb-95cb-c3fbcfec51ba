<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>第三方服务、SDK采集个人信息清单</title>
	<link rel="stylesheet" href="../css/index.css">
</head>

<body>
	<div>
		<p class=MsoNormal align=center style='text-align:center;text-indent:21.0pt'><b><span
			lang=ZH-CN style='font-size:12.0pt;font-family:FangSong'>第三方服务、</span></b><b><span
			style='font-size:12.0pt;font-family:FangSong'>SDK<span lang=ZH-CN>采集个人信息清单</span></span></b></p>
			
			<p class=MsoNormal style='text-indent:21.0pt'><span style='font-size:12.0pt;
			font-family:FangSong'>&nbsp;</span></p>
			
			<p class=MsoNormal style='text-indent:21.0pt'><span style='font-size:12.0pt;
			font-family:FangSong'>&nbsp;</span></p>
			
			<p class=MsoNormal style='text-indent:21.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>为保证平台相关功能的实现和应用的安全稳定运行，我们及</span><span
			style='font-size:12.0pt;font-family:FangSong'>/<span lang=ZH-CN>或合作方会接入由第三方技术服务（“三方服务、</span>SDK<span
			lang=ZH-CN>”）以实现该目的。</span></span></p>
			
			<p class=MsoNormal style='text-indent:21.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>我们及</span><span style='font-size:
			12.0pt;font-family:FangSong'>/<span lang=ZH-CN>或合作方会对第三方服务进行严格的安全检测，并要求合作方采取严格的措施来保护您的个人数据。如您认为第三方服务的行为侵犯了您的权利，您可以通过本说明中对应第三方的隐私政策中所列的联系方式与第三方服务供应商取得联系。如无法联系到对方的，可告知<b>本平台客服【</b></span><b>4008602166<span
			lang=ZH-CN>】</span></b><span lang=ZH-CN>，本平台客服会将您的诉求转达给供应商。</span></span></p>
			
			<p class=MsoNormal style='text-indent:21.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>在满足新的服务需求及业务功能变更时，我们及</span><span
			style='font-size:12.0pt;font-family:FangSong'>/<span lang=ZH-CN>或合作方可能会调整我们接入的第三方服务，并及时在本目录中向您公开说明接入第三方服务的最新情况。请您注意，第三方服务可能因为版本升级等原因导致收集信息类型发生变化，请以其公示的官方说明为准。</span></span></p>
			
			<p class=MsoNormal style='text-indent:21.0pt'><span style='font-size:12.0pt;
			font-family:FangSong'>&nbsp;</span></p>
			
			<p class=MsoNormal style='text-indent:21.0pt'><b><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>一、</span></b><b><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>第三方服务个人信息收集清单</span></b></p>
			
			<p class=MsoNormal><span style='font-size:12.0pt;font-family:FangSong'>&nbsp;</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>服务名称：电子签名服务</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>开发者：杭州天谷信息科技有限公司</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>基本功能：电子签章</span></b></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>使用场景：</span><span lang=ZH
			style='font-size:12.0pt;font-family:FangSong'>登录、申请金融产品时相关协议签署</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>使用目的：允许收集用户姓名、手机号、身份证号实现认证签署功能</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>收集信息：</span></b><b><u><span
			lang=ZH style='font-size:12.0pt;font-family:FangSong'>姓名、手机号、身份证号</span></u></b></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>隐私政策：</span><span
			style='font-size:12.0pt;font-family:FangSong'><a
			href="https://www.esign.cn/gw/fwxy">https://www.esign.cn/gw/fwxy</a></span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span style='font-size:12.0pt;
			font-family:FangSong'>&nbsp;</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>开发者：南京三百云信息科技有限公司</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>基本功能：</span></b><b><span lang=ZH
			style='font-size:12.0pt;font-family:FangSong'>根据用户提供的车辆信息做车产估值</span></b></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>使用场景：</span><span lang=ZH
			style='font-size:12.0pt;font-family:FangSong'>进件订单初评</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>使用目的：</span><span lang=ZH
			style='font-size:12.0pt;font-family:FangSong'>对客户进件资质进行评估</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>收集信息：</span></b><b><u><span
			lang=ZH style='font-size:12.0pt;font-family:FangSong'>车牌号、车</span></u></b><b><u><span
			lang=ZH-CN style='font-size:12.0pt;font-family:FangSong'>架号码、</span></u></b><b><u><span
			lang=ZH style='font-size:12.0pt;font-family:FangSong'>车辆状态、所在城市</span></u></b></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>隐私政策：</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span style='font-size:12.0pt;
			font-family:FangSong'><a
			href="https://dingjia.che300.com/h5pages/H5pages/cappPrivacyPolicy">https://dingjia.che300.com/h5pages/H5pages/cappPrivacyPolicy</a></span></p>
			
			<p class=MsoNormal align=left style='text-align:left;text-indent:24.0pt'><span
			style='font-size:12.0pt;font-family:FangSong'>&nbsp;</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>开发者：贵州数据宝网络科技有限公司</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>基本功能：鉴权服务</span></b></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>使用场景：</span><span lang=ZH
			style='font-size:12.0pt;font-family:FangSong'>获取用户授权范围内的个人基础数据、车保信息</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>使用目的：</span><span lang=ZH
			style='font-size:12.0pt;font-family:FangSong'>对客户进件资质进行评估</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>收集信息：<u>姓名、身份证号、手机号</u></span></b><b><u><span
			lang=ZH style='font-size:12.0pt;font-family:FangSong'>、车牌号</span></u></b></p>
			
			<p class=MsoNormal align=left style='text-align:left;text-indent:24.0pt'><span
			lang=ZH-CN style='font-size:12.0pt;font-family:FangSong'>隐私政策：</span><span
			style='font-size:12.0pt;font-family:FangSong'><a
			href="https://www.chinadatapay.com/privacy">https://www.chinadatapay.com/privacy</a></span></p>
			
			<p class=MsoNormal align=left style='text-align:left;text-indent:24.0pt'><span
			style='font-size:12.0pt;font-family:FangSong'>&nbsp;</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>开发者：上海创蓝云智信息科技股份有限公司</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>基本功能：</span></b><b><span lang=ZH
			style='font-size:12.0pt;font-family:FangSong'>根据用户提供的车辆信息做车产估值</span></b></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>使用场景：</span><span lang=ZH
			style='font-size:12.0pt;font-family:FangSong'>进件订单初评</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>使用目的：</span><span lang=ZH
			style='font-size:12.0pt;font-family:FangSong'>对客户进件资质进行评估</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>收集信息：</span></b><b><u><span
			lang=ZH style='font-size:12.0pt;font-family:FangSong'>车牌号、车</span></u></b><b><u><span
			lang=ZH-CN style='font-size:12.0pt;font-family:FangSong'>架号码、</span></u></b><b><u><span
			lang=ZH style='font-size:12.0pt;font-family:FangSong'>车辆状态、所在城市</span></u></b></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>隐私政策：</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span style='font-size:12.0pt;
			font-family:FangSong'><a
			href="https://doc.chuanglan.com/document/SD092WS4AD4WOJEY">https://doc.chuanglan.com/document/SD092WS4AD4WOJEY</a></span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span style='font-size:12.0pt;
			font-family:FangSong'>&nbsp;</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>开发者：上海创蓝云智信息科技股份有限公司</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>基本功能：鉴权服务</span></b></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>使用场景：</span><span lang=ZH
			style='font-size:12.0pt;font-family:FangSong'>获取用户授权范围内的个人基础数据、车保信息</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>使用目的：</span><span lang=ZH
			style='font-size:12.0pt;font-family:FangSong'>对客户进件资质进行评估</span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>收集信息：<u>姓名、身份证号、手机号</u></span></b><b><u><span
			lang=ZH style='font-size:12.0pt;font-family:FangSong'>、车牌号</span></u></b></p>
			
			<p class=MsoNormal style='text-indent:24.0pt'><span style='font-size:12.0pt;
			font-family:FangSong'><a
			href="https://doc.chuanglan.com/document/SD092WS4AD4WOJEY">https://doc.chuanglan.com/document/SD092WS4AD4WOJEY</a></span></p>
			
			<p class=MsoNormal align=left style='text-align:left;text-indent:24.0pt'><span
			style='font-size:12.0pt;font-family:FangSong'>&nbsp;</span></p>
			
			<p class=MsoNormal align=left style='text-align:left;text-indent:24.0pt'><span
			style='font-size:12.0pt;font-family:FangSong'>&nbsp;</span></p>
			
			<p class=MsoNormal><span style='font-size:12.0pt;font-family:FangSong'>&nbsp;</span></p>
			
			<p class=MsoNormal style='text-indent:21.0pt'><b><span lang=ZH-CN
			style='font-size:12.0pt;font-family:FangSong'>二、</span></b><b><span
			style='font-size:12.0pt;font-family:FangSong'>SDK<span lang=ZH-CN>个人信息收集清单</span></span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>友盟移动统计</span></b><b><span style='font-family:FangSong'>SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>设备信息（</span><span style='font-family:FangSong'>Android
			ID/IDFA/OAID/OpenUDID/GUID /IMEI/IMSI/ICCID<span lang=ZH-CN>）、 网络信息、位置信息</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>获取</span><span style='font-family:FangSong'>Android
			ID/IDFA/OAID/OpenUDID/GUID /IMEI/IMSI/ICCID <span lang=ZH-CN>，采集设备标识信息用于生成脱敏的终端用户设备标识，提供设备应用性能监控服务；获取网络信息，检测联网方式，在网络异常状态避免数据发送，节约流量和电量，查看</span>wifi<span
			lang=ZH-CN>状态，选择最优联网链路以节省流量和电量；获取位置信息，为开发者提供反舞弊功能，提出作弊设备，同时校正用户的地域分布数据，使报表数据更加准确。</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户进入</span><span style='font-family:FangSong'>app<span lang=ZH-CN>时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>友盟同欣（北京）科技有限公司、北京锐讯灵通科技有限公司</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span style='font-family:FangSong'><a
			href="https://www.umeng.com/policy">https://www.umeng.com/policy</a></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span
			style='font-family:FangSong'>&nbsp;</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>快手</span></b><b><span style='font-family:FangSong'>SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>设备品牌、设备型号、软件系统版本、存储信息、运营商信息、设备时区、设备语言、网络信息等基础信息；</span><span
			style='font-family:FangSong'>IME<span lang=ZH-CN>、</span>MEID<span lang=ZH-CN>、</span>OAID<span
			lang=ZH-CN>、</span>Android ID<span lang=ZH-CN>、</span>IMSI<span lang=ZH-CN>、</span>ICCID<span
			lang=ZH-CN>、</span>IP&nbsp;<span lang=ZH-CN>地址、</span>WIFI<span lang=ZH-CN>信息、</span>MAC<span
			lang=ZH-CN>地址、</span>GPS&nbsp;<span lang=ZH-CN>位置信息、基站信息、</span>WIFI&nbsp;<span
			lang=ZH-CN>信息；应用安装列表；传感器信息</span>&nbsp;(<span lang=ZH-CN>加速度、重力、陀螺仪传感器</span>)<span
			lang=ZH-CN>、</span>sim<span lang=ZH-CN>卡激活信息，已安装应用列表</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>向用户展示快手广告</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>在用户进入活动选择观看时使用<span style='color:black;background:#FDFDFD'>，在静默状态下或在后台运行时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>北京快手广告有限公司</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span style='font-family:FangSong'><a
			href="https://u.kuaishou.com/home/<USER>/detail/1337/1400/1405" target="_blank">https://u.kuaishou.com/home/<USER>/detail/1337/1400/1405</a></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span
			style='font-family:FangSong'>&nbsp;</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>增长营销套件</span></b><b><span style='font-family:FangSong'>SDK</span></b><span
			style='font-family:FangSong;color:black;background:white'><br>
			</span><b><span lang=ZH-CN style='font-family:FangSong'>涉及个人信息：</span></b><span
			lang=ZH-CN style='font-family:FangSong'>设备标识符（IMEI、IDFA、IDFV、Android ID、MAC、OAID、ICCID、设备硬件序列号等相关信息）、应用信息、设备参数及系统信息（设备品牌、设备型号、操作系统、操作系统api版本、屏幕分辨率、磁盘使用情况、内存使用情况、CPU信息、时区、系统语言等）、运营商信息、移动设备国家编码（MCC）、移动设备网络编码（MNC）、IP地址等相关信息。</span><span
			style='font-family:FangSong;color:black;background:white'><br>
			</span><b><span lang=ZH-CN style='font-family:FangSong'>使用目的：</span></b><span
			lang=ZH-CN style='font-family:FangSong'>数据分析</span><span style='font-family:
			FangSong;color:black;background:white'><br>
			</span><b><span lang=ZH-CN style='font-family:FangSong'>使用场景：</span></b><span
			lang=ZH-CN style='font-family:FangSong'>用户数据采集与分析</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>北京火山引擎科技有限公司<br>
			</span><b><span lang=ZH-CN style='font-family:FangSong'>收集方式：</span></b><span
			lang=ZH-CN style='font-family:FangSong'>SDK自行采集</span><span style='font-family:
			FangSong;color:black;background:white'><br>
			</span><b><span lang=ZH-CN style='font-family:FangSong'>隐私政策链接：</span></b><span
			style='font-family:FangSong;color:black;background:white'><a
			href="https://www.volcengine.com/docs/6285/72216">https://www.volcengine.com/docs/6285/72216</a></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span lang=ZH-CN
			style='font-family:FangSong'>&nbsp;</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span
			style='font-family:FangSong'>OpenInstall&nbsp;SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>设备品牌、设备型号、操作系统版本、</span><span style='font-family:
			FangSong'>IP<span lang=ZH-CN>地址、粘贴板（剪切板）、</span>Android&nbsp;ID<span
			lang=ZH-CN>、</span>GAID<span lang=ZH-CN>、设备序列号（</span>SerialNumber<span
			lang=ZH-CN>）、设备传感器（传感器数量）、本地存储权限、</span>IMEI<span lang=ZH-CN>、</span>IDFA<span
			lang=ZH-CN>、</span>OAID<span lang=ZH-CN>、</span>Mac<span lang=ZH-CN>地址</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>渠道统计、应用统计分析、作弊防护、移动广告监测</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户进入</span><span style='font-family:FangSong'>app<span lang=ZH-CN>时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>深圳市分秒网络科技有限公司</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span style='font-family:FangSong'><a
			href="https://www.openinstall.io/privacy.html" target="_blank">https://www.openinstall.io/privacy.html</a></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span
			style='font-family:FangSong'>&nbsp;</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>创蓝闪验</span></b><b><span style='font-family:FangSong'>SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>设备信息、位置信息、网络信息、</span><span style='font-family:
			FangSong'>SDK<span lang=ZH-CN>认证相关请求结果、手机号码</span>&nbsp;<span lang=ZH-CN>设备信息：设备标识符（</span>IMEI<span
			lang=ZH-CN>、</span>IDFA<span lang=ZH-CN>、</span>Android&nbsp;ID<span
			lang=ZH-CN>、</span>MAC<span lang=ZH-CN>、</span>OAID<span lang=ZH-CN>等相关信息）、应用信息（应用崩溃信息、通知开关状态、软件列表等相关信息）、设备参数及系统信息（设备类型、设备型号、操作系统及硬件相关信息）</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>用于实现</span><span style='font-family:
			FangSong;color:black;background:#FDFDFD'>App<span lang=ZH-CN>一键登录和验证用户手机号码与本机流量卡号是否一致的服务</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>调用</span><span style='font-family:FangSong'>SDK<span lang=ZH-CN>预取号接口、获取一键登录</span>/<span
			lang=ZH-CN>验证</span>token<span lang=ZH-CN>接口</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>上海创蓝云智信息科技股份有限公司 </span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span style='font-family:FangSong'><a
			href="https://doc.chuanglan.com/document/T98GE1KYCZDMJDHN">https://doc.chuanglan.com/document/T98GE1KYCZDMJDHN</a></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span
			style='font-family:FangSong'>&nbsp;</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>号码认证</span></b><b><span style='font-family:FangSong'>-android&nbsp;</span></b></p>
			
			<p class=MsoNormal align=left style='text-align:left;text-indent:24.0pt'><b><span
			lang=ZH-CN style='font-size:12.0pt;font-family:FangSong'>涉及个人信息：</span></b><span
			lang=ZH-CN style='font-size:12.0pt;font-family:FangSong'>设备信息、位置信息、网络信息、网络类型、切换</span><span
			style='font-size:12.0pt;font-family:FangSong'>Wi-fi<span lang=ZH-CN>和蜂窝网络通道、</span>SDK<span
			lang=ZH-CN>认证相关请求结果、手机号码</span>&nbsp;<span lang=ZH-CN>设备信息：设备标识符（</span>IMEI<span
			lang=ZH-CN>、</span>IDFA<span lang=ZH-CN>、</span>Android&nbsp;ID<span
			lang=ZH-CN>、</span>MAC<span lang=ZH-CN>、</span>OAID<span lang=ZH-CN>等相关信息）、应用信息（应用崩溃信息、通知开关状态、软件列表等相关信息）、设备参数及系统信息（设备类型、设备型号、操作系统及硬件相关信息）</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>用于实现</span><span style='font-family:
			FangSong;color:black;background:#FDFDFD'>App<span lang=ZH-CN>一键登录和验证用户手机号码与本机流量卡号是否一致的服务</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>调用</span><span style='font-family:FangSong'>SDK<span lang=ZH-CN>预取号接口、获取一键登录</span>/<span
			lang=ZH-CN>验证</span>token<span lang=ZH-CN>接口</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>阿里巴巴云计算（北京）有限公司 </span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p class=MsoNormal align=left style='text-align:left;text-indent:24.0pt'><b><span
			lang=ZH-CN style='font-size:12.0pt;font-family:FangSong'>合作方隐私政策：</span></b><span
			style='font-size:12.0pt;font-family:FangSong'><a
			href="https://terms.aliyun.com/legal-agreement/terms/suit_bu1_ali_cloud/suit_bu1_ali_cloud202112211045_86198.html?spm=a2c4g.11186623.0.0.727039eb0HflH4%20">https://terms.aliyun.com/legal-agreement/terms/suit_bu1_ali_cloud/suit_bu1_ali_cloud202112211045_86198.html?spm=a2c4g.11186623.0.0.727039eb0HflH4
			</a></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span
			style='font-family:FangSong'>&nbsp;</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>中国移动（阿里云号码认证</span></b><b><span style='font-family:
			FangSong'>SDK<span lang=ZH-CN>集成）</span></span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>网络类型、网络地址、运营商类型、本机号码信息手机设备类型、手机操作系统、硬件厂商</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>使用手机快速登录，简化登录操作</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户登录</span><span style='font-family:FangSong'>app<span lang=ZH-CN>时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>中国移动通信集团有限公司</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span class=MsoHyperlink><span
			style='font-family:FangSong'><a href="https://wap.cmpassport.com/resources/html/contract2.html">https://wap.cmpassport.com/resources/html/contract2.html</a></span></span></p>
			
			<p class=MsoNormal align=left style='text-align:left;text-indent:24.0pt'><span
			style='font-size:12.0pt;font-family:FangSong'>&nbsp;</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>联通认证</span></b><b><span style='font-family:FangSong'>SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>网络类型、网络地址、运营商类型、本机号码信息手机设备类型、手机操作系统、硬件厂商</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>使用手机快速登录，简化登录操作</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户登录</span><span style='font-family:FangSong'>app<span lang=ZH-CN>时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>联通在线信息科技有限公司</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span class=MsoHyperlink><span
			style='font-family:FangSong'><a href="https://msv6.wosms.cn/html/oauth/protocol2.html">https://msv6.wosms.cn/html/oauth/protocol2.html</a></span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span
			style='font-family:FangSong'>&nbsp;</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span
			style='font-family:FangSong'>cn.com.chinatelecom(<span lang=ZH-CN>电信</span>;<span
			lang=ZH-CN>中国电信</span>)</span></b><span style='font-family:FangSong'>&nbsp;<b>&nbsp;SDK</b></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>网络类型、网络地址、运营商类型、本机号码信息手机设备类型、手机操作系统、硬件厂商</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>使用手机快速登录，简化登录操作</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户登录</span><span style='font-family:FangSong'>app<span lang=ZH-CN>时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>中国电信</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span style='font-family:FangSong'><a
			href="https://e.189.cn/sdk/agreement/content.do?type=main&amp;appKey=E_189&amp;hidetop=true&amp;returnUrl="
			target="_blank">https://e.189.cn/sdk/agreement/content.do?type=main&amp;appKey=E_189&amp;hidetop=true&amp;returnUrl=</a></span></p>
			
			<p class=MsoNormal style='text-indent:24.0pt;line-height:150%'><span
			lang=ZH-CN style='font-size:12.0pt;line-height:150%;font-family:FangSong'>&nbsp;</span></p>
			
			<p style='text-indent:24.0pt'><b><span style='font-family:FangSong;color:black;
			background:#FDFDFD'>com.netease.nis(<span lang=ZH-CN>网易易盾</span>;<span
			lang=ZH-CN>移动安全联盟</span>;MSA)&nbsp;SDK </span></b></p>
			
			<p style='text-indent:24.0pt'><b><span lang=ZH-CN style='font-family:FangSong;
			color:black;background:#FDFDFD'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong;color:black;background:#FDFDFD'>设备唯一识别码（设备序列号、</span><span
			style='font-family:FangSong;color:black;background:#FDFDFD'>MEID<span
			lang=ZH-CN>、</span>IDFV<span lang=ZH-CN>）、设备标识符（</span>IMEI<span lang=ZH-CN>、</span>IDFA<span
			lang=ZH-CN>、</span>Android&nbsp;ID<span lang=ZH-CN>、</span>MAC<span lang=ZH-CN>、</span>OAID<span
			lang=ZH-CN>等相关信息）、设备品牌、设备名称、设备型号及设备系统类型、详细设置及版本信息；网络相关信息：设备</span>IP<span
			lang=ZH-CN>地址、路由器标识（</span>BSSID<span lang=ZH-CN>、</span>SSID<span lang=ZH-CN>、</span>MAC<span
			lang=ZH-CN>）、网络运营商信息、网络连接类型及状态；操作信息：最终用户开机、点击、安装行为信息、传感器信息（运动传感器、位置传感器、环境传感器）；</span>Android<span
			lang=ZH-CN>应用的应用包名、应用名称及版本号、应用签名信息、应用进程名 </span></span></p>
			
			<p style='text-indent:24.0pt'><b><span lang=ZH-CN style='font-family:FangSong;
			color:black;background:#FDFDFD'>使用目的：</span></b><span lang=ZH-CN
			style='font-family:FangSong;color:black;background:#FDFDFD'>用于应用加固</span><span
			style='font-family:FangSong;color:black;background:#FDFDFD'>&nbsp;<span
			lang=ZH-CN>；判定设备是否处于异常状态；判定当前用户是否有异常操作行为；判定当前应用运行是否存在异常 </span></span></p>
			
			<p style='text-indent:24.0pt'><b><span lang=ZH-CN style='font-family:FangSong;
			color:black;background:#FDFDFD'>使用场景：</span></b><span lang=ZH-CN
			style='font-family:FangSong;color:black;background:#FDFDFD'>用户使用</span><span
			style='font-family:FangSong;color:black;background:#FDFDFD'>app<span
			lang=ZH-CN>时 </span></span></p>
			
			<p style='text-indent:24.0pt'><b><span lang=ZH-CN style='font-family:FangSong;
			color:black;background:#FDFDFD'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong;color:black;background:#FDFDFD'>杭州网易智企科技有限公司 </span></p>
			
			<p style='text-indent:24.0pt'><b><span lang=ZH-CN style='font-family:FangSong;
			color:black;background:#FDFDFD'>收集方式：</span></b><span style='font-family:FangSong;
			color:black;background:#FDFDFD'>SDK<span lang=ZH-CN>采集 </span></span></p>
			
			<p style='text-indent:24.0pt'><b><span lang=ZH-CN style='font-family:FangSong;
			color:black;background:#FDFDFD'>合作方隐私政策：</span></b><span style='font-family:
			FangSong;color:black;background:#FDFDFD'><a
			href="https://dun.163.com/clause/privacy%20">https://dun.163.com/clause/privacy
			</a></span></p>
			
			<p style='text-indent:24.0pt'>&nbsp;</p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>腾讯浏览服务</span></b><b><span style='font-family:FangSong'>X5<span
			lang=ZH-CN>网页引擎</span>SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>设备信息（设备型号、操作系统、</span><span style='font-family:
			FangSong'>CPU<span lang=ZH-CN>类型）、应用信息（宿主应用包名，版本号）、</span>Wi-Fi<span
			lang=ZH-CN>状态和参数、</span>IP<span lang=ZH-CN>地址、</span>AndroidID<span lang=ZH-CN>、</span>CellID<span
			lang=ZH-CN>、</span>OAID<span lang=ZH-CN>、</span>BSSID<span lang=ZH-CN>、</span>SSID</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>用于</span><span style='font-family:
			FangSong;color:black;background:#FDFDFD'>app<span lang=ZH-CN>内加载网页，浏览器内核升级和禁用细化控制，浏览器内核升级及禁用细化控制需要，判断</span>Wi-Fi<span
			lang=ZH-CN>是否连接，进行网络优化，服务异常定位和识别黑产等虚假设备，进行安全风控和服务稳定性建设，服务稳定性分析，进行服务稳定性建设</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户使用</span><span style='font-family:FangSong'>app<span lang=ZH-CN>内网页时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>深圳市腾讯计算机系统有限公司</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span class=MsoHyperlink><span
			style='font-family:FangSong'><a
			href="https://rule.tencent.com/rule/1c4e2b4b-d0f6-4a75-a5c6-1cfce00a390d">https://rule.tencent.com/rule/1c4e2b4b-d0f6-4a75-a5c6-1cfce00a390d</a></span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span
			class=MsoHyperlink><span style='font-family:FangSong'><span style='text-decoration:
			 none'>&nbsp;</span></span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>腾讯云慧眼卡证</span></b><b><span style='font-family:
			FangSong'> OCR SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>身份证照片正反面、手机型号、手机品牌、 </span><span style='font-family:
			FangSong'>Android<span lang=ZH-CN>系统版本、</span>Android<span lang=ZH-CN>系统</span>api<span
			lang=ZH-CN>等级、厂商系统版本、</span>cpu<span lang=ZH-CN>架构类 型、设备是否</span>root<span
			lang=ZH-CN>、磁盘空间占用大小、</span>sdcard<span lang=ZH-CN>空间占用大小、内存空间占用大小、网络类型、应用当前正在运行的进程名和</span>PID</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>实名认证，对</span><span style='font-family:
			FangSong;color:black;background:#FDFDFD'>SDK<span lang=ZH-CN>运行时发生的故障问题进行排查，分析</span>SDK<span
			lang=ZH-CN>和设备本身的异常和性能问题，定位和分析异常</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户上传身份证照片申请金融产品时</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>腾讯云计算（北京）有限责任公司</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span class=MsoHyperlink><span
				style='font-family:FangSong'><a href="https://privacy.qq.com/document/preview/f52d26cb7ea34e35accb40426c758bf3">https://privacy.qq.com/document/preview/f52d26cb7ea34e35accb40426c758bf3</a></span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span
			style='font-family:FangSong'>&nbsp;</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>创蓝卡证</span></b><b><span style='font-family:FangSong'>
			OCR SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>身份证照片正反面、手机型号、手机品牌、</span><span style='font-family:
			FangSong'> Android<span lang=ZH-CN>系统版本、</span>Android<span lang=ZH-CN>系统</span>api<span
			lang=ZH-CN>等级、厂商系统版本、</span>cpu<span lang=ZH-CN>架构类 型、设备是否</span>root<span
			lang=ZH-CN>、磁盘空间占用大小、</span>sdcard<span lang=ZH-CN>空间占用大小、内存空间占用大小、网络类型、应用当前正在运行的进程名和</span>PID</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>实名认证，对</span><span style='font-family:
			FangSong;color:black;background:#FDFDFD'>SDK<span lang=ZH-CN>运行时发生的故障问题进行排查，分析</span>SDK<span
			lang=ZH-CN>和设备本身的异常和性能问题，定位和分析异常</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户上传身份证照片申请金融产品时</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>上海创蓝云智信息科技股份有限公司</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span class=MsoHyperlink><a
			href="https://doc.chuanglan.com/document/4V9HECG0MKCPKMV1">https://doc.chuanglan.com/document/4V9HECG0MKCPKMV1</a></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span
			style='font-family:FangSong'>&nbsp;</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span
			style='font-family:FangSong'>&nbsp;</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>小米推送</span></b><b><span style='font-family:FangSong'>
			SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span lang=ZH-CN
			style='font-family:FangSong'>设备标识符</span><span style='font-family:FangSong'>(ANDROID_ID<span
			lang=ZH-CN>，</span>IMEI/MEID/ESN<span lang=ZH-CN>，硬件序列号</span>)<span
			lang=ZH-CN>、</span>WiFi<span lang=ZH-CN>信息、应用信息</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>用于消息推送，为用户提供个性化推送服务</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户使用</span><span style='font-family:FangSong'>app<span lang=ZH-CN>时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>小米科技有限责任公司</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span class=MsoHyperlink><span
			style='font-family:FangSong'><a href="https://dev.mi.com/distribute/doc/details?pId=1819">https://dev.mi.com/distribute/doc/details?pId=1819</a></span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span
			style='font-family:FangSong'>&nbsp;</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>华为推送</span></b><b><span style='font-family:FangSong'>
			SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span lang=ZH-CN
			style='font-family:FangSong'>设备标识符</span><span style='font-family:FangSong'>(ANDROID_ID<span
			lang=ZH-CN>，</span>IMEI/MEID/ESN<span lang=ZH-CN>，硬件序列号</span>)<span
			lang=ZH-CN>、</span>WiFi<span lang=ZH-CN>信息、应用信息</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>用于消息推送，为用户提供个性化推送服务</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户使用</span><span style='font-family:FangSong'>app<span lang=ZH-CN>时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>华为终端有限公司</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span class=MsoHyperlink><span
			style='font-family:FangSong'><a href="https://developer.huawei.com/consumer/cn/doc/HMSCore-Guides/sdk-data-security-0000001050042177">https://developer.huawei.com/consumer/cn/doc/HMSCore-Guides/sdk-data-security-0000001050042177</a></span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span
			style='font-family:FangSong'>&nbsp;</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span
			style='font-family:FangSong'>OPP0<span lang=ZH-CN>推送</span> SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span lang=ZH-CN
			style='font-family:FangSong'>设备标识符</span><span style='font-family:FangSong'>(ANDROID_ID<span
			lang=ZH-CN>，</span>IMEI/MEID/ESN<span lang=ZH-CN>，硬件序列号</span>)<span
			lang=ZH-CN>、</span>WiFi<span lang=ZH-CN>信息、应用信息</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>用于消息推送，为用户提供个性化推送服务</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户使用</span><span style='font-family:FangSong'>app<span lang=ZH-CN>时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span style='font-family:FangSong'>OPPO<span
			lang=ZH-CN>广东移动通信有限公司</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span class=MsoHyperlink><span
			style='font-family:FangSong'><a
			href="https://open.oppomobile.com/wiki/doc#id=10288">https://open.oppomobile.com/wiki/doc#id=10288</a></span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span
			class=MsoHyperlink><span style='font-family:FangSong'><span style='text-decoration:
			 none'>&nbsp;</span></span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span
			style='font-family:FangSong'>VIVO<span lang=ZH-CN>推送</span> SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span lang=ZH-CN
			style='font-family:FangSong'>设备标识符</span><span style='font-family:FangSong'>(ANDROID_ID<span
			lang=ZH-CN>，</span>IMEI/MEID/ESN<span lang=ZH-CN>，硬件序列号</span>)<span
			lang=ZH-CN>、</span>WiFi<span lang=ZH-CN>信息、应用信息</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>用于消息推送，为用户提供个性化推送服务</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户使用</span><span style='font-family:FangSong'>app<span lang=ZH-CN>时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>维沃移动通信有限公司</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span class=MsoHyperlink><span
			style='font-family:FangSong'><a
			href="https://dev.vivo.com.cn/documentCenter/doc/366">https://dev.vivo.com.cn/documentCenter/doc/366</a></span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span
			class=MsoHyperlink><span style='font-family:FangSong'><span style='text-decoration:
			 none'>&nbsp;</span></span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>极光推送</span></b><b><span style='font-family:FangSong'>S</span></b><b><span
			lang=ZH-CN style='font-family:FangSong'>DK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>运营信息、网络状态信息、</span><span style='font-family:FangSong'>iOS<span
			lang=ZH-CN>广告标识符（</span>IDFA<span lang=ZH-CN>）、</span>MAC<span lang=ZH-CN>地址、国际移动设备识别码（</span>IMEI<span
			lang=ZH-CN>）、匿名设备标识符（</span>OAID)<span lang=ZH-CN>、国际移动用户识别码（</span>IMSI<span
			lang=ZH-CN>）、应用列表信息、基站信息、社交平台</span>OpenID<span lang=ZH-CN>、地理位置</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>用于消息推送，为用户提供个性化推送服务</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户使用</span><span style='font-family:FangSong'>app<span lang=ZH-CN>时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>深圳市和讯华谷信息技术有限公司 </span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span class=MsoHyperlink><span
			style='font-family:FangSong'><a href="https://www.jiguang.cn/license/privacy">https://www.jiguang.cn/license/privacy</a></span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span
			class=MsoHyperlink><span style='font-family:FangSong'><span style='text-decoration:
			 none'>&nbsp;</span></span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>个推消息推送SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>设备平台、设备厂商、设备品牌、设备型号及系统版本、</span><span
			style='font-family:FangSong'>OAID<span lang=ZH-CN>、</span>AndroidID<span
			lang=ZH-CN>、</span>WIFI<span lang=ZH-CN>连接信息、运营商信息、</span>DHCP<span lang=ZH-CN>、</span>SSID<span
			lang=ZH-CN>、</span>BSSID<span lang=ZH-CN>、</span>iOS<span lang=ZH-CN>广告标识符（</span>IDFA<span
			lang=ZH-CN>）、</span>MAC<span lang=ZH-CN>地址、国际移动设备识别码（</span>IMEI<span
			lang=ZH-CN>）、国际移动用户识别码（</span>IMSI<span lang=ZH-CN>）、基站信息、社交平台</span>OpenID<span
			lang=ZH-CN>、地理位置、已安装应用列表</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>用</span><span lang=ZH-CN
			style='font-family:FangSong'>于消息推送，为用户提供个性化推送服务。</span><span lang=ZH-CN
			style='font-family:FangSong'>在您授权同意后，获取访问您的本机设备标识符（</span><span
			style='font-family:FangSong'>IMEI<span lang=ZH-CN>、</span>IDFA<span lang=ZH-CN>、</span>Android
			ID<span lang=ZH-CN>、</span>OAID<span lang=ZH-CN>相关信息）、</span>WIFI<span
			lang=ZH-CN>连接信息及</span>SSID<span lang=ZH-CN>、</span>BSSID<span lang=ZH-CN>、用于生成唯一的推送目标</span>ID<span
			lang=ZH-CN>（</span>CID<span lang=ZH-CN>）和设备</span>ID<span lang=ZH-CN>（</span>GID<span
			lang=ZH-CN>），以此保证消息推送的准确下发和消息设备的准确识别。获取已安装应用列表、<span style='color:black;
			background:white'>位置相关信息，</span>应国家有关部门要求，为了提升移动互联网技术服务社会公益的功能，发挥</span> APP <span
			lang=ZH-CN>消息推送在灾害预警方面的作用，当发生地震及气象等重大灾害时，通过开发者的</span>APP <span lang=ZH-CN>推送通道实时向受影响地区的用户下发相关的灾害通知信息</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户使用</span><span style='font-family:FangSong'>app<span lang=ZH-CN>时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>每日互动股份有限公司 </span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span class=MsoHyperlink><b><span
			lang=ZH-CN style='font-family:FangSong'><a href="https://docs.getui.com/privacy/">https://docs.getui.com/privacy/</a></span></b></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span lang=ZH-CN
			style='font-family:FangSong;color:blue;background:#FDFDFD'>&nbsp;</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>SDK极光魔链 SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong;color:black;background:#FDFDFD'>运营信息、网络状态信息、iOS广告标识符（IDFA）、MAC地址、国际移动设备识别码（IMEI）、匿名设备标识符（OAID)、国际移动用户识别码（IMSI）、应用列表信息、基站信息、社交平台OpenID、地理位置</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的</span></b><span lang=ZH-CN style='font-family:
			FangSong;color:black;background:#FDFDFD'>：深度链接服务，用于实现链接跳转功能</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户使用</span><span style='font-family:FangSong'>app<span lang=ZH-CN>时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>深圳市和讯华谷信息技术有限公司 </span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span class=MsoHyperlink><span
			style='font-family:FangSong'><a href="https://www.jiguang.cn/license/privacy">https://www.jiguang.cn/license/privacy</a></span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span
			class=MsoHyperlink><span style='font-family:FangSong'><span style='text-decoration:
			 none'>&nbsp;</span></span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>buglySDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>运营信息、网络状态信息、</span><span style='font-family:FangSong'>iOS<span
			lang=ZH-CN>广告标识符（</span>IDFA<span lang=ZH-CN>）、</span>MAC<span lang=ZH-CN>地址、国际移动设备识别码（</span>IMEI<span
			lang=ZH-CN>）、匿名设备标识符（</span>OAID)<span lang=ZH-CN>、国际移动用户识别码（</span>IMSI<span
			lang=ZH-CN>）、应用列表信息、基站信息、社交平台</span>OpenID<span lang=ZH-CN>、地理位置</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用于收集用户崩溃数据功能，方便解决用户</span><span style='font-family:FangSong'>app<span
			lang=ZH-CN>功能崩溃问题</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户使用</span><span style='font-family:FangSong'>app<span lang=ZH-CN>时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>深圳市腾讯计算机系统有限公司 </span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span class=MsoHyperlink><span
			style='font-family:FangSong'><a href="https://privacy.qq.com/document/priview/fbd2c3f898df4c1c869925dd49d57827">https://privacy.qq.com/document/priview/fbd2c3f898df4c1c869925dd49d57827</a></span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span lang=ZH-CN
			style='font-family:FangSong'>&nbsp;</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>MMKV SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>系统运营信息、网络状态信息、iOS广告标识符(IDFA)、应用列表信息、社交平台openID、地理位置信息</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span lang=ZH-CN
			style='font-family:FangSong'>设备信息，获取sd卡数据</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>为用户提供个性化推送服务，缓存服务记录用户登录状态</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户使用</span><span style='font-family:FangSong'>app<span lang=ZH-CN>时</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方主体：</span></b><span lang=ZH-CN
			style='font-family:FangSong'>深圳市腾讯计算机系统有限公司</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span lang=ZH-CN
			style='font-family:FangSong'><a
			href="https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/aY5BAtRiO1BpoHxo">https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/aY5BAtRiO1BpoHxo</a></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><span lang=ZH-CN
			style='font-family:FangSong'>&nbsp;</span></p>
			
			<p class=MsoNormal align=left style='text-align:left;text-indent:24.0pt'><b><span
			lang=ZH-CN style='font-size:12.0pt;font-family:FangSong'>巨量</span></b><b><span
			lang=ZH-CN style='font-size:12.0pt;font-family:FangSong'>引擎激活</span></b><b><span
			style='font-size:12.0pt;font-family:FangSong'>SDK</span></b></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>涉及个人信息：</span></b><span style='font-family:FangSong'>PV4<span
			lang=ZH-CN>、</span>IPV6<span lang=ZH-CN>、</span>UA<span lang=ZH-CN>、本地时间、</span>AndroidID<span
			lang=ZH-CN>、</span>IMEI<span lang=ZH-CN>、</span>OAID<span lang=ZH-CN>、设备品牌、型号、操作系统、软件系统版本、分辨率、网络访问模式、设备语言、</span>MCC<span
			lang=ZH-CN>移动国家码、</span>MNC<span lang=ZH-CN>移动网络码、</span>CPU<span lang=ZH-CN>信息、浏览器的类型和版本号、操作、使用、服务日志基础信息、应用内用户唯一</span>ID<span
			lang=ZH-CN>、</span>WiFi<span lang=ZH-CN>路由器</span>MAC<span lang=ZH-CN>地址（</span>BSSID<span
			lang=ZH-CN>）、设备的</span>MAC<span lang=ZH-CN>地址、设备标识符（如</span>IMSI<span
			lang=ZH-CN>、</span>ICCID<span lang=ZH-CN>、</span>GAID<span lang=ZH-CN>（仅</span>GMS<span
			lang=ZH-CN>服务）、</span>MEID<span lang=ZH-CN>、设备序列号</span>build_serial<span
			lang=ZH-CN>、应用信息、广告信息、位置信息</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用目的：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用于广告投放归因检测、广告投放统计</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>使用场景：</span></b><span lang=ZH-CN style='font-family:
			FangSong'>用户使用</span><span style='font-family:FangSong'>app<span lang=ZH-CN>时</span></span></p>
			
			<p class=MsoNormal align=left style='text-align:left;text-indent:24.0pt'><b><span
			lang=ZH-CN style='font-size:12.0pt;font-family:FangSong'>合作方主体：</span></b><span
			lang=ZH-CN style='font-size:12.0pt;font-family:FangSong'>北京巨量引擎网络技术有限公司</span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>收集方式：</span></b><span style='font-family:FangSong'>SDK<span
			lang=ZH-CN>采集</span></span></p>
			
			<p align=left style='text-align:left;text-indent:24.0pt'><b><span lang=ZH-CN
			style='font-family:FangSong'>合作方隐私政策：</span></b><span lang=ZH-CN
			style='font-family:FangSong'><a
			href="https://bytedance.larkoffice.com/docx/NtbVd73CIoFLsuxqsyPcjHrdnFB">https://bytedance.larkoffice.com/docx/NtbVd73CIoFLsuxqsyPcjHrdnFB</a></span></p>
			
			<p class=MsoNormal><span style='font-size:12.0pt;font-family:FangSong'>&nbsp;</span></p>
	</div>
</body>

</html>