# 定义执行步骤
stages:
  - build
  - deploy

发布:
  stage: build
  tags:
    - ${RUNNER_TAGS}
  environment: ${DEPLOY_ENV}
  retry:
    max: 2
    when:
      - script_failure
      - job_execution_timeout
  script:
    - |-
      echo "------------------------------上传OSS------------------------------"
      ossutil64  config -e oss-cn-hangzhou.aliyuncs.com -i $AccessKeyID -k $AccessKeySecret
      ossutil64 sync -f protocol/ oss://$OSS_BUCKET/protocol
      echo "已同步文件资源"
      # ossutil64 sync -f --include=index.html  protocol/ oss://$OSS_BUCKET/protocol
      # echo "设置元属性"
      # ossutil64 set-meta oss://$OSS_BUCKET/index.html Cache-Control:no-cache --update
      # ossutil64 set-meta oss://$OSS_BUCKET/index.html x-oss-meta-commited:${CI_COMMIT_SHORT_SHA} --update
