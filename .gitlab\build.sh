PATH=${NODE_VERSION}:$PATH
node -v | xargs echo "node version: "

if  grep -q "3DA1" /proc/net/tcp ; then
    # 使用代理
    echo "加速器模式: 本地代理"
    PROXY='--proxy http://127.0.0.1:15777'
else
    PROXY=''
    echo "加速器模式: 使用淘宝镜像加速"
    npm config set registry https://registry.npmmirror.com/ 
fi


# 构建信息
set -x
set -e

echo "------------------------------安装模块------------------------------"
${VUE_INSTALL} ${PROXY}
${VUE_BUILD}:${DEPLOY_ENV}

