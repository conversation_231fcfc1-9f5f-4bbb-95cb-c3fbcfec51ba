function getQueryString(name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
  var r = window.location.search.substr(1).match(reg);
  if (r != null) return decodeURI(r[2]);
  return null;
}
// 公司主体
const companyBody = {
  '1': {
    name: '优卡数信',
    company: '成都优卡数信信息科技有限公司',
    address: '中国（四川）自由贸易试验区成都高新区天华二路219号C区6栋3层',
    tel: '028-85230110',
    kefu: '400-6030-386',
  },
}
// 设置品牌
function setBrand(title) {
  let brandName = getQueryString('brandName')
  if (brandName) {
    let brandNameArr = document.getElementsByClassName('brandName')
    for (const i in brandNameArr) {
      brandNameArr[i].innerText = brandName
    }
    let platform = brandName === '大业优享' ? '小程序' : 'H5'
    let platformArr = document.getElementsByClassName('platform')
    for (const i in platformArr) {
      platformArr[i].innerText = platform
    }
  }
}

// 设置我方公司主体信息
function setCompanyMain () {
  const yxType = getQueryString('yxType')
  const companyObj = yxType && companyBody[yxType]
  if (companyObj) {
    let companyArr = document.getElementsByClassName('company')
    for (const i in companyArr) {
      companyArr[i].innerText = companyObj.company
    }
    let shortNameArr = document.getElementsByClassName('shortName')
    for (const i in shortNameArr) {
      shortNameArr[i].innerText = companyObj.name
    }
    let addressArr = document.getElementsByClassName('address')
    for (const i in addressArr) {
      addressArr[i].innerText = companyObj.address
    }
    let telArr = document.getElementsByClassName('tel')
    for (const i in telArr) {
      telArr[i].innerText = companyObj.tel
    }
    let kefuArr = document.getElementsByClassName('kefu')
    for (const i in kefuArr) {
      kefuArr[i].innerText = companyObj.kefu
    }
  }
}

// 设置三方主体
function setMainPart() {
  let mainPart = getQueryString('mainPart')
  if (mainPart) {
    let mainPartArr = document.getElementsByClassName('mainPart')
    for (const i in mainPartArr) {
      mainPartArr[i].innerText = mainPart
    }
  }
  let companyAddress = getQueryString('companyAddress')
  if (companyAddress) {
    let companyAddressArr = document.getElementsByClassName('companyAddress')
    for (const i in companyAddressArr) {
      companyAddressArr[i].innerText = companyAddress
    }
  }
  let organPhone = getQueryString('organPhone')
  if (organPhone) {
    let organPhoneArr = document.getElementsByClassName('organPhone')
    for (const i in organPhoneArr) {
      organPhoneArr[i].innerText = organPhone
    }
  } 
}

// 设置三方平台名称
function setBusiPlatName () {
  let busiPlatName = getQueryString('busiPlatName')
  if (busiPlatName) {
    let busiPlatNameArr = document.getElementsByClassName('busiPlatName')
    for (const i in busiPlatNameArr) {
      busiPlatNameArr[i].innerText = busiPlatName
    }
  }
}

// 设置个人信息
function setPersonalMessage() {
  let personalMsgHtml = getQueryString('personalMsgHtml')
  if (personalMsgHtml) {
    let personalMsgBox = document.getElementById('personalMsg')
    if (personalMsgBox) {
      personalMsgBox.innerHTML = decodeURI(personalMsgHtml)
    }
  }
}